<div class="registration-form-container">
  <div class="scrollable-content">
    <div class="header">
      <div class="logo-wrapper">
        <img [src]="logoSrc" alt="Logo" class="logo">
      </div>
      <h1 class="page-title">Register</h1>
    </div>
    
    <div class="content">
      <form [formGroup]="registrationForm" (ngSubmit)="onSubmit()">
      <div class="form-group">
        <input 
          type="text" 
          formControlName="name" 
          placeholder="Name"
          class="form-control"
          [class.error]="getControl('name')?.invalid && getControl('name')?.touched">
      </div>

      <div class="form-group">
        <input 
          type="text" 
          formControlName="surname" 
          placeholder="Surname"
          class="form-control"
          [class.error]="getControl('surname')?.invalid && getControl('surname')?.touched">
      </div>

      <div class="form-group">
        <input 
          type="tel" 
          formControlName="mobile" 
          placeholder="Mobile Number"
          class="form-control"
          maxlength="10"
          autocomplete="tel"
          [class.error]="getControl('mobile')?.invalid && getControl('mobile')?.touched">
      </div>

      <div class="form-group">
        <input 
          type="email" 
          formControlName="email" 
          placeholder="Email"
          class="form-control"
          autocomplete="email"
          [class.error]="getControl('email')?.invalid && getControl('email')?.touched">
      </div>

      <div class="form-group">
        <input 
          type="text" 
          formControlName="dateOfBirth" 
          placeholder="Date of Birth"
          class="form-control"
          onfocus="(this.type='date')"
          onblur="(this.type='text')"
          [class.error]="getControl('dateOfBirth')?.invalid && getControl('dateOfBirth')?.touched">
      </div>

      <div class="form-group">
        <input 
          type="text" 
          formControlName="idNumber" 
          placeholder="ID Number"
          class="form-control"
          maxlength="13"
          [class.error]="getControl('idNumber')?.invalid && getControl('idNumber')?.touched">
      </div>

      <div class="form-group">
        <div class="password-input-wrapper">
          <input 
            [type]="passwordVisible ? 'text' : 'password'" 
            formControlName="password" 
            placeholder="Password"
            class="form-control"
            [class.error]="getControl('password')?.invalid && getControl('password')?.touched">
          <button 
            type="button"
            class="password-toggle"
            (click)="passwordVisible = !passwordVisible">
            <span class="icon">{{ passwordVisible ? '👁️' : '👁️‍🗨️' }}</span>
          </button>
        </div>
      </div>

      <div class="form-group">
        <div class="password-input-wrapper">
          <input 
            [type]="confirmPasswordVisible ? 'text' : 'password'" 
            formControlName="confirmPassword" 
            placeholder="Confirm Password"
            class="form-control"
            [class.error]="getControl('confirmPassword')?.invalid && getControl('confirmPassword')?.touched">
          <button 
            type="button"
            class="password-toggle"
            (click)="confirmPasswordVisible = !confirmPasswordVisible">
            <span class="icon">{{ confirmPasswordVisible ? '👁️' : '👁️‍🗨️' }}</span>
          </button>
        </div>
      </div>
      
      <button 
        type="submit" 
        class="btn-primary"
        [disabled]="registrationForm.invalid || loading">
        {{ loading ? 'Submitting...' : 'Submit' }}
      </button>
    </form>
    </div>
  </div>
  
  <nav class="bottom-nav">
    <button class="nav-item" disabled>
      <span class="icon">⬜</span>
    </button>
  </nav>
</div>