import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { BalanceDetailComponent as MobileBalanceDetailComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-balance-detail-page',
  standalone: true,
  imports: [CommonModule, MobileBalanceDetailComponent],
  template: `
    <app-awards-balance-detail
      [balanceInfo]="balanceInfo"
      [partnerLogo]="partnerLogo"
      [partnerColor]="partnerColor"
      (navigationClicked)="onNavigate($event)"
      (backClicked)="onBack()">
    </app-awards-balance-detail>
  `,
  styles: []
})
export class AwardsBalanceDetailComponent implements OnInit {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';
  partnerId = '';
  partnerLogo = 'PNA';
  partnerColor = '#e53935';

  balanceInfo = {
    name: '<PERSON>',
    balance: 50336,
    currency: 'R',
    transactions: []
  };

  constructor(
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.partnerId = params['partnerId'] || 'pna';
      // Update partner info based on partnerId
      this.updatePartnerInfo(this.partnerId);
    });
  }

  private updatePartnerInfo(partnerId: string) {
    const partnerInfo: { [key: string]: { logo: string, color: string } } = {
      'pna': { logo: 'PNA', color: '#e53935' },
      'buildit': { logo: 'Build IT', color: '#c62828' },
      'leroymerlin': { logo: 'LEROY MERLIN', color: '#66bb6a' }
    };

    const info = partnerInfo[partnerId] || partnerInfo['pna'];
    this.partnerLogo = info.logo;
    this.partnerColor = info.color;
  }

  onBack() {
    this.router.navigate(['/public/awards/dashboard']);
  }

  onNavigate(route: any) {
    switch(route) {
      case 'home':
        this.router.navigate(['/public/awards/dashboard']);
        break;
      case 'profile':
        // Navigate to profile when implemented
        break;
      case 'card':
        this.router.navigate(['/public/awards/partner-card'], {
          queryParams: { partnerId: this.partnerId }
        });
        break;
      case 'shops':
        this.router.navigate(['/public/awards/program-selection']);
        break;
      case 'transactions':
        this.router.navigate(['/public/awards/transaction'], {
          queryParams: { partnerId: this.partnerId }
        });
        break;
      default:
        break;
    }
  }
}