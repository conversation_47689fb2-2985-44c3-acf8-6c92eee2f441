import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ProgramSelectionComponent as MobileProgramSelectionComponent, AwardsAuthService, AwardsProgramsService } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-program-selection',
  standalone: true,
  imports: [CommonModule, MobileProgramSelectionComponent],
  template: `
    <app-program-selection
      [enrolledProgramIds]="enrolledProgramIds"
      (programSelected)="onSelectProgram($event)"
      (navigationClicked)="onNavigate($event)">
    </app-program-selection>
  `,
  styles: []
})
export class AwardsProgramSelectionComponent implements OnInit {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';
  enrolledProgramIds: string[] = [];

  constructor(
    private router: Router,
    private awardsAuth: AwardsAuthService,
    private programsService: AwardsProgramsService
  ) {}

  ngOnInit() {
    const currentUser = this.awardsAuth.getCurrentUser();
    if (currentUser) {
      this.userId = currentUser.id;
      const enrolledPrograms = this.programsService.getEnrolledPrograms(this.userId);
      this.enrolledProgramIds = enrolledPrograms.map(p => p.partnerId);
    }
  }

  onSelectProgram(programId: string) {
    this.router.navigate(['/public/awards/terms-acceptance'], {
      queryParams: { partnerId: programId }
    });
  }

  onNavigate(route: any) {
    switch(route) {
      case 'home':
        this.router.navigate(['/public/awards/dashboard']);
        break;
      case 'profile':
        // Navigate to profile when implemented
        break;
      case 'card':
        this.router.navigate(['/public/awards/partner-card']);
        break;
      case 'transactions':
        this.router.navigate(['/public/awards/transaction']);
        break;
      default:
        break;
    }
  }
}