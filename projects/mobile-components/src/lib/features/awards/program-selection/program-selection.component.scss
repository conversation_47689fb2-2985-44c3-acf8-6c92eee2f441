.program-selection-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #1a1a1a;
  color: #ffffff;

  .header {
    display: flex;
    justify-content: center;
    padding: 40px 0 30px;

    .logo {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background-color: #333;
      padding: 15px;
    }
  }

  .content {
    flex: 1;
    padding: 0 20px;
    
    .title {
      font-size: 24px;
      font-weight: 300;
      margin: 0 0 30px;
      text-align: center;
    }

    .programs-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
      max-width: 400px;
      margin: 0 auto;

      .program-card {
        aspect-ratio: 1;
        border: none;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        min-height: 100px;

        &:hover:not(:disabled) {
          transform: scale(1.05);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        &.enrolled {
          opacity: 0.7;
          
          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.3);
          }
        }

        &:nth-child(3) {
          grid-column: 1 / -1;
          aspect-ratio: 2;
        }

        .program-logo {
          color: white;
          font-weight: 700;
          font-size: 16px;
          text-align: center;
          text-transform: uppercase;
          letter-spacing: 1px;
          padding: 10px;
        }

        .enrolled-badge {
          position: absolute;
          top: 10px;
          right: 10px;
          background-color: rgba(0, 0, 0, 0.5);
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 10px;
          font-weight: 600;
          text-transform: uppercase;
        }
      }
    }
  }

  .bottom-nav {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background-color: #2a2a2a;
    border-top: 1px solid #3a3a3a;
    padding: 8px 0;
    position: sticky;
    bottom: 0;

    .nav-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: none;
      border: none;
      color: #666;
      cursor: pointer;
      padding: 8px;
      transition: all 0.3s ease;

      .icon {
        font-size: 20px;
        margin-bottom: 2px;
      }

      &:hover {
        color: #888;
      }

      &.active {
        color: #ffc107;
      }
    }
  }
}