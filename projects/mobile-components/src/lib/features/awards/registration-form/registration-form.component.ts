import { Component, EventEmitter, Input, Output, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

export interface RegistrationData {
  mobile: string;
  otp: string;
  name: string;
  surname: string;
  email: string;
  dateOfBirth: string;
  idNumber: string;
  password: string;
}

@Component({
  selector: 'app-registration-form',
  templateUrl: './registration-form.component.html',
  styleUrls: ['./registration-form.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, IonicModule]
})
export class RegistrationFormComponent implements OnInit {
  @Input() logoSrc = 'assets/images/logo.png';
  @Input() mobile = '';
  @Input() otp = '';
  @Output() registrationSubmitted = new EventEmitter<RegistrationData>();
  @Output() backClicked = new EventEmitter<void>();
  
  registrationForm: FormGroup;
  loading = false;
  passwordVisible = false;
  confirmPasswordVisible = false;

  constructor(private fb: FormBuilder) {
    this.registrationForm = this.fb.group({
      mobile: ['', [Validators.required, Validators.pattern(/^[0-9]{9,10}$/)]],
      name: ['', [Validators.required, Validators.minLength(2)]],
      surname: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      dateOfBirth: ['', [Validators.required]],
      idNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{13}$/)]],
      password: ['', [Validators.required, Validators.minLength(4)]],
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });
  }

  ngOnInit() {
    if (this.mobile) {
      this.registrationForm.patchValue({ mobile: this.mobile });
    }
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');
    
    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
    } else if (confirmPassword?.hasError('passwordMismatch')) {
      confirmPassword.setErrors(null);
    }
    
    return null;
  }

  onSubmit(): void {
    if (this.registrationForm.valid) {
      this.loading = true;
      const formData = this.registrationForm.value;
      const registrationData: RegistrationData = {
        ...formData,
        otp: this.otp
      };
      this.registrationSubmitted.emit(registrationData);
    }
  }

  onBack(): void {
    this.backClicked.emit();
  }

  getControl(name: string) {
    return this.registrationForm.get(name);
  }
}