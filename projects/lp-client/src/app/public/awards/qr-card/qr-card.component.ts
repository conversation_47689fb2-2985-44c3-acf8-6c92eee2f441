import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { QrCardComponent as MobileQrCardComponent, AwardsAuthService } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-qr-card',
  standalone: true,
  imports: [CommonModule, MobileQrCardComponent],
  template: `
    <app-qr-card
      [cardInfo]="cardInfo"
      (navigationClicked)="onNavigate($event)"
      (backClicked)="onBack()">
    </app-qr-card>
  `,
  styles: []
})
export class AwardsQrCardComponent implements OnInit {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';
  partnerId = '';

  cardInfo = {
    partnerId: 'buildit',
    partnerName: 'Build It',
    partnerLogo: 'Build IT',
    backgroundColor: '#c62828',
    userName: '<PERSON>',
    userNumber: '1237896114'
  };

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private awardsAuth: AwardsAuthService
  ) {}

  ngOnInit() {
    // Get current user
    const currentUser = this.awardsAuth.getCurrentUser();
    if (currentUser) {
      this.cardInfo.userName = `${currentUser.name} ${currentUser.surname}`;
      this.cardInfo.userNumber = currentUser.mobile;
    }

    this.route.queryParams.subscribe(params => {
      this.partnerId = params['partnerId'] || 'pna';
      // Update cardInfo based on partnerId
      const partnerInfo = this.getPartnerInfo(this.partnerId);
      this.cardInfo = {
        ...this.cardInfo,
        partnerId: this.partnerId,
        partnerName: partnerInfo.name,
        partnerLogo: partnerInfo.logo,
        backgroundColor: partnerInfo.color
      };
    });
  }

  private getPartnerInfo(partnerId: string): { name: string, logo: string, color: string } {
    const partners: { [key: string]: { name: string, logo: string, color: string } } = {
      'pna': { name: 'Pick n Pay', logo: 'PNA', color: '#e53935' },
      'buildit': { name: 'Build It', logo: 'Build IT', color: '#c62828' },
      'leroymerlin': { name: 'Leroy Merlin', logo: 'LEROY MERLIN', color: '#66bb6a' }
    };
    return partners[partnerId] || partners['pna'];
  }

  onBack() {
    this.router.navigate(['/public/awards/partner-card'], {
      queryParams: { partnerId: this.partnerId }
    });
  }

  onNavigate(route: any) {
    switch(route) {
      case 'home':
        this.router.navigate(['/public/awards/dashboard']);
        break;
      case 'profile':
        // Navigate to profile when implemented
        break;
      case 'card':
        this.router.navigate(['/public/awards/partner-card']);
        break;
      case 'shops':
        this.router.navigate(['/public/awards/program-selection']);
        break;
      case 'transactions':
        this.router.navigate(['/public/awards/transaction']);
        break;
      default:
        break;
    }
  }
}