<div class="dashboard-container">
  <div class="header">
    <img [src]="logoSrc" alt="Logo" class="logo">
  </div>
  
  <div class="content">
    <h1 class="greeting">Hello {{ user.name }}</h1>
    
    <div class="balance-section">
      <p class="balance-label">Check your Balance and Rewards.</p>
    </div>

    <div class="partners-grid">
      <button 
        *ngFor="let program of enrolledPrograms" 
        class="partner-card"
        [style.background-color]="program.backgroundColor"
        (click)="onPartnerClick(program.partnerId)">
        <span class="partner-logo">{{ program.partnerLogo }}</span>
      </button>
      
      <!-- Show message if no programs enrolled -->
      <div *ngIf="enrolledPrograms.length === 0" class="no-programs-message">
        <p>You haven't joined any programs yet.</p>
        <p>Tap the shops icon below to join programs.</p>
      </div>
    </div>
  </div>

  <nav class="bottom-nav">
    <button class="nav-item active" (click)="onNavigate('home')">
      <span class="icon">🏠</span>
    </button>
    <button class="nav-item" (click)="onNavigate('profile')">
      <span class="icon">👤</span>
    </button>
    <button class="nav-item" (click)="onNavigate('card')">
      <span class="icon">💳</span>
    </button>
    <button class="nav-item" (click)="onNavigate('shops')">
      <span class="icon">🏪</span>
    </button>
    <button class="nav-item" (click)="onNavigate('transactions')">
      <span class="icon">📋</span>
    </button>
  </nav>
</div>