<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Loyalty Plus Mobile App</title>
    <base href="/" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=0"
    />
    <meta name="color-scheme" content="light dark" />
    <link rel="icon" type="image/x-icon" href="assets/images/logo.png" />
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
      rel="stylesheet"
    />
    <script>
      // Only load Google Maps for non-demo deployments
      if (!window.location.hostname.includes('goloyalty.cloud')) {
        var script = document.createElement('script');
        script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyBw8kDBn79ehC9HnA1hjy9BCBsQwYXNnr0&libraries=places';
        script.defer = true;
        script.async = true;
        document.head.appendChild(script);
        
        var markerScript = document.createElement('script');
        markerScript.src = 'assets/js/markerclusterer.min.js';
        document.head.appendChild(markerScript);
      }
    </script>
  </head>
  <body>
    <app-root></app-root>
  </body>
</html>
