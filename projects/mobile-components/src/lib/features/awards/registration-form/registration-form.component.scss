.registration-form-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #1a1a1a;
  color: #ffffff;
  position: relative;

  .scrollable-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 80px;
  }

  .header {
    text-align: center;
    padding: 40px 20px 30px;

    .logo-wrapper {
      display: inline-flex;
      justify-content: center;
      align-items: center;
      width: 80px;
      height: 80px;
      background-color: #2a2a2a;
      border-radius: 50%;
      margin-bottom: 20px;
    }

    .logo {
      width: 50px;
      height: 50px;
    }

    .page-title {
      font-size: 20px;
      font-weight: 400;
      margin: 0;
      color: #ffffff;
    }
  }

  .content {
    padding: 0 20px 20px;
    max-width: 400px;
    width: 100%;
    margin: 0 auto;

    form {
      display: flex;
      flex-direction: column;
    }

    .form-group {
      margin-bottom: 12px;

      .form-control {
        width: 100%;
        padding: 14px;
        background-color: #2a2a2a;
        border: none;
        border-radius: 4px;
        color: #ffffff;
        font-size: 14px;
        transition: all 0.3s ease;

        &::placeholder {
          color: #666;
          font-size: 12px;
        }

        &:focus {
          outline: none;
          background-color: #333;
        }

        &.error {
          border: 1px solid #f44336;
        }

        &[type="date"] {
          color-scheme: dark;
          
          &::-webkit-calendar-picker-indicator {
            filter: invert(0.8);
          }
        }
      }

      .password-input-wrapper {
        position: relative;
        display: flex;
        align-items: center;

        .password-toggle {
          position: absolute;
          right: 10px;
          background: none;
          border: none;
          color: #666;
          cursor: pointer;
          padding: 5px;
          font-size: 18px;

          &:hover {
            color: #888;
          }
        }
      }
    }

    .btn-primary {
      width: 100%;
      padding: 16px;
      background-color: #c62828;
      color: #ffffff;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-top: 20px;

      &:hover:not(:disabled) {
        background-color: #b71c1c;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }

  .bottom-nav {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #2a2a2a;
    border-top: 1px solid #3a3a3a;
    padding: 16px 0;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;

    .nav-item {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 50px;
      height: 50px;
      background: none;
      border: none;
      color: #666;
      cursor: pointer;

      .icon {
        font-size: 24px;
      }

      &:disabled {
        cursor: not-allowed;
        opacity: 0.5;
      }
    }
  }
}