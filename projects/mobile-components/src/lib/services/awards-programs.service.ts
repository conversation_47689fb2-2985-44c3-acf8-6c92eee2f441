import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface EnrolledProgram {
  partnerId: string;
  partnerName: string;
  partnerLogo: string;
  backgroundColor: string;
  enrolledDate: string;
}

@Injectable({
  providedIn: 'root'
})
export class AwardsProgramsService {
  private readonly STORAGE_KEY = 'awards_enrolled_programs';
  
  private enrolledProgramsSubject = new BehaviorSubject<EnrolledProgram[]>([]);
  public enrolledPrograms$ = this.enrolledProgramsSubject.asObservable();

  constructor() {
    // Load enrolled programs from localStorage on init
    this.loadEnrolledPrograms();
  }

  // Get all enrolled programs for current user
  getEnrolledPrograms(userId: string): EnrolledProgram[] {
    const allPrograms = this.getAllPrograms();
    return allPrograms[userId] || [];
  }

  // Enroll in a new program
  enrollInProgram(userId: string, program: Omit<EnrolledProgram, 'enrolledDate'>): boolean {
    const allPrograms = this.getAllPrograms();
    const userPrograms = allPrograms[userId] || [];
    
    // Check if already enrolled
    if (userPrograms.find(p => p.partnerId === program.partnerId)) {
      return false; // Already enrolled
    }
    
    // Add new program
    const newProgram: EnrolledProgram = {
      ...program,
      enrolledDate: new Date().toISOString()
    };
    
    userPrograms.push(newProgram);
    allPrograms[userId] = userPrograms;
    
    this.savePrograms(allPrograms);
    this.enrolledProgramsSubject.next(userPrograms);
    
    return true;
  }

  // Check if user is enrolled in a specific program
  isEnrolled(userId: string, partnerId: string): boolean {
    const userPrograms = this.getEnrolledPrograms(userId);
    return userPrograms.some(p => p.partnerId === partnerId);
  }

  // Load enrolled programs for a specific user
  loadEnrolledPrograms(userId?: string): void {
    if (userId) {
      const userPrograms = this.getEnrolledPrograms(userId);
      this.enrolledProgramsSubject.next(userPrograms);
    }
  }

  // Get all programs from localStorage
  private getAllPrograms(): { [userId: string]: EnrolledProgram[] } {
    const stored = localStorage.getItem(this.STORAGE_KEY);
    return stored ? JSON.parse(stored) : {};
  }

  // Save programs to localStorage
  private savePrograms(programs: { [userId: string]: EnrolledProgram[] }): void {
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(programs));
  }

  // Clear all enrollment data (for testing)
  clearAllData(): void {
    localStorage.removeItem(this.STORAGE_KEY);
    this.enrolledProgramsSubject.next([]);
  }
}