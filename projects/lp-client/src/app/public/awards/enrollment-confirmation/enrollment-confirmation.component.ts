import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { EnrollmentConfirmationComponent as MobileEnrollmentConfirmationComponent, AwardsAuthService, AwardsProgramsService } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-enrollment-confirmation',
  standalone: true,
  imports: [CommonModule, MobileEnrollmentConfirmationComponent],
  template: `
    <app-enrollment-confirmation
      [enrollmentInfo]="enrollmentInfo"
      (switchProgram)="onSwitchProgram()"
      (navigationClicked)="onNavigate($event)">
    </app-enrollment-confirmation>
  `,
  styles: []
})
export class AwardsEnrollmentConfirmationComponent implements OnInit {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';
  partnerId = '';

  enrollmentInfo = {
    programId: 'leroymerlin',
    programName: '<PERSON>',
    programLogo: 'LEROY MERLIN',
    backgroundColor: '#66bb6a',
    userName: 'Marius',
    memberNumber: '135638765'
  };

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private awardsAuth: AwardsAuthService,
    private programsService: AwardsProgramsService
  ) {}

  ngOnInit() {
    // Get current user
    const currentUser = this.awardsAuth.getCurrentUser();
    if (currentUser) {
      this.enrollmentInfo.userName = currentUser.name;
      this.enrollmentInfo.memberNumber = currentUser.mobile;
      this.userId = currentUser.id;
    }

    this.route.queryParams.subscribe(params => {
      this.partnerId = params['partnerId'] || 'pna';
      // Update enrollmentInfo based on partnerId
      const partnerInfo = this.getPartnerInfo(this.partnerId);
      this.enrollmentInfo = {
        ...this.enrollmentInfo,
        programId: this.partnerId,
        programName: partnerInfo.name,
        programLogo: partnerInfo.logo,
        backgroundColor: partnerInfo.color
      };
      
      // Enroll the user in the program
      if (this.userId && this.partnerId) {
        const enrolled = this.programsService.enrollInProgram(this.userId, {
          partnerId: this.partnerId,
          partnerName: partnerInfo.name,
          partnerLogo: partnerInfo.logo,
          backgroundColor: partnerInfo.color
        });
        console.log('Enrollment result:', enrolled, 'User ID:', this.userId, 'Partner ID:', this.partnerId);
      }
    });
  }

  private getPartnerInfo(partnerId: string): { name: string, logo: string, color: string } {
    const partners: { [key: string]: { name: string, logo: string, color: string } } = {
      'pna': { name: 'Pick n Pay', logo: 'PNA', color: '#e53935' },
      'buildit': { name: 'Build It', logo: 'Build IT', color: '#c62828' },
      'leroymerlin': { name: 'Leroy Merlin', logo: 'LEROY MERLIN', color: '#66bb6a' }
    };
    return partners[partnerId] || partners['pna'];
  }

  onContinue() {
    this.router.navigate(['/public/awards/dashboard']);
  }

  onSwitchProgram() {
    this.router.navigate(['/public/awards/program-selection']);
  }

  onNavigate(route: any) {
    switch(route) {
      case 'home':
        this.router.navigate(['/public/awards/dashboard']);
        break;
      case 'profile':
        // Navigate to profile when implemented
        break;
      case 'card':
        this.router.navigate(['/public/awards/partner-card']);
        break;
      case 'shops':
        this.router.navigate(['/public/awards/program-selection']);
        break;
      case 'transactions':
        this.router.navigate(['/public/awards/transaction']);
        break;
      default:
        break;
    }
  }
}