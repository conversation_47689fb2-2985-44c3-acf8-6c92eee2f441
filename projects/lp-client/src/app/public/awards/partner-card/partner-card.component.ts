import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { PartnerCardComponent as MobilePartnerCardComponent, AwardsAuthService } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-partner-card',
  standalone: true,
  imports: [CommonModule, MobilePartnerCardComponent],
  template: `
    <app-partner-card
      [cardInfo]="cardInfo"
      (navigationClicked)="onNavigate($event)"
      (cardClicked)="onUseCard($event)">
    </app-partner-card>
  `,
  styles: []
})
export class AwardsPartnerCardComponent implements OnInit {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';
  partnerId = '';

  cardInfo = {
    partnerId: 'buildit',
    partnerName: 'Build It',
    partnerLogo: 'Build IT',
    backgroundColor: '#c62828',
    userName: 'Marius',
    balance: 2116,
    currency: 'R',
    transactions: []
  };

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private awardsAuth: AwardsAuthService
  ) {}

  ngOnInit() {
    // Get current user
    const currentUser = this.awardsAuth.getCurrentUser();
    if (currentUser) {
      this.cardInfo.userName = currentUser.name;
      // Generate demo balance
      this.cardInfo.balance = Math.floor(Math.random() * 50000) + 50000;
    }

    this.route.queryParams.subscribe(params => {
      this.partnerId = params['partnerId'] || 'pna';
      // Update cardInfo based on partnerId
      const partnerInfo = this.getPartnerInfo(this.partnerId);
      this.cardInfo = {
        ...this.cardInfo,
        partnerId: this.partnerId,
        partnerName: partnerInfo.name,
        partnerLogo: partnerInfo.logo,
        backgroundColor: partnerInfo.color
      };
    });
  }

  private getPartnerInfo(partnerId: string): { name: string, logo: string, color: string } {
    const partners: { [key: string]: { name: string, logo: string, color: string } } = {
      'pna': { name: 'Pick n Pay', logo: 'PNA', color: '#e53935' },
      'buildit': { name: 'Build It', logo: 'Build IT', color: '#c62828' },
      'leroymerlin': { name: 'Leroy Merlin', logo: 'LEROY MERLIN', color: '#66bb6a' }
    };
    return partners[partnerId] || partners['pna'];
  }

  onUseCard(event: any) {
    this.router.navigate(['/public/awards/qr-card'], {
      queryParams: { partnerId: this.partnerId }
    });
  }

  onNavigate(route: any) {
    switch(route) {
      case 'home':
        this.router.navigate(['/public/awards/dashboard']);
        break;
      case 'profile':
        // Navigate to profile when implemented
        break;
      case 'shops':
        this.router.navigate(['/public/awards/program-selection']);
        break;
      case 'transactions':
        this.router.navigate(['/public/awards/transaction'], {
          queryParams: { partnerId: this.partnerId }
        });
        break;
      default:
        break;
    }
  }
}