<div class="program-selection-container">
  <div class="header">
    <img [src]="logoSrc" alt="Logo" class="logo">
  </div>
  
  <div class="content">
    <h1 class="title">I want to join...</h1>
    
    <div class="programs-grid">
      <button 
        *ngFor="let program of programs" 
        class="program-card"
        [style.background-color]="program.backgroundColor"
        (click)="onProgramClick(program.id)"
        [disabled]="isEnrolled(program.id)"
        [class.enrolled]="isEnrolled(program.id)">
        <span class="program-logo">{{ program.logo }}</span>
        <span class="enrolled-badge" *ngIf="isEnrolled(program.id)">Enrolled</span>
      </button>
    </div>
  </div>

  <nav class="bottom-nav">
    <button class="nav-item active" (click)="onNavigate('home')">
      <span class="icon">🏠</span>
    </button>
    <button class="nav-item" (click)="onNavigate('profile')">
      <span class="icon">👤</span>
    </button>
    <button class="nav-item" (click)="onNavigate('card')">
      <span class="icon">💳</span>
    </button>
    <button class="nav-item" (click)="onNavigate('shops')">
      <span class="icon">🏪</span>
    </button>
    <button class="nav-item" (click)="onNavigate('transactions')">
      <span class="icon">📋</span>
    </button>
  </nav>
</div>