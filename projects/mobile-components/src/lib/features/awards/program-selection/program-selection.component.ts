import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

export interface Program {
  id: string;
  name: string;
  logo: string;
  backgroundColor: string;
  enrolled: boolean;
}

@Component({
  selector: 'app-program-selection',
  templateUrl: './program-selection.component.html',
  styleUrls: ['./program-selection.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule]
})
export class ProgramSelectionComponent {
  @Input() logoSrc = 'assets/images/logo.png';
  @Input() enrolledProgramIds: string[] = [];
  @Output() programSelected = new EventEmitter<string>();
  @Output() navigationClicked = new EventEmitter<string>();

  programs: Program[] = [
    {
      id: 'pna',
      name: 'Pick n Pay',
      logo: 'PNA',
      backgroundColor: '#e53935',
      enrolled: false
    },
    {
      id: 'buildit',
      name: 'Build It',
      logo: 'Build IT',
      backgroundColor: '#c62828',
      enrolled: false
    },
    {
      id: 'leroy<PERSON>lin',
      name: '<PERSON>',
      logo: 'LEROY MERLIN',
      backgroundColor: '#66bb6a',
      enrolled: false
    }
  ];

  isEnrolled(programId: string): boolean {
    return this.enrolledProgramIds.includes(programId);
  }

  onProgramClick(programId: string): void {
    if (!this.isEnrolled(programId)) {
      this.programSelected.emit(programId);
    }
  }

  onNavigate(route: string): void {
    this.navigationClicked.emit(route);
  }
}