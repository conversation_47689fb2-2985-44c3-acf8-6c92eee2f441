.login-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #1a1a1a;
  color: #ffffff;
  padding-bottom: 80px; // Account for fixed bottom nav

  .header {
    display: flex;
    justify-content: center;
    padding: 60px 0 40px;

    .logo {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background-color: #333;
      padding: 15px;
    }
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-width: 400px;
    width: 100%;
    margin: 0 auto;
    padding: 0 20px;

    .form-group {
      margin-bottom: 20px;

      .form-control {
        width: 100%;
        padding: 16px;
        background-color: #2a2a2a;
        border: none;
        border-bottom: 2px solid #3a3a3a;
        border-radius: 0;
        color: #ffffff;
        font-size: 14px;
        font-weight: 500;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;

        &::placeholder {
          color: #888;
          text-transform: uppercase;
          font-size: 12px;
        }

        &:focus {
          outline: none;
          border-bottom-color: #e53935;
          background-color: #2a2a2a;
        }

        &.error {
          border-bottom-color: #f44336;
        }
      }

      .password-input-wrapper {
        position: relative;
        display: flex;
        align-items: center;

        .password-toggle {
          position: absolute;
          right: 10px;
          background: none;
          border: none;
          color: #666;
          cursor: pointer;
          padding: 5px;
          font-size: 18px;

          &:hover {
            color: #888;
          }
        }
      }
    }

    .btn-primary {
      width: 100%;
      padding: 16px;
      background-color: #e53935;
      color: #ffffff;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-top: 20px;

      &:hover:not(:disabled) {
        background-color: #c62828;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }

  .bottom-nav {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #2a2a2a;
    border-top: 1px solid #3a3a3a;
    padding: 16px 0;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;

    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: none;
      border: none;
      color: #666;
      cursor: pointer;
      padding: 8px 16px;
      transition: all 0.3s ease;

      .icon {
        font-size: 24px;
        margin-bottom: 2px;
      }

      &:hover {
        color: #888;
      }

      &.active {
        color: #e53935;
      }
    }
  }
}