import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { TermsAcceptanceComponent as MobileTermsAcceptanceComponent } from '@projects/mobile-components';

@Component({
  selector: 'app-awards-terms-acceptance',
  standalone: true,
  imports: [CommonModule, MobileTermsAcceptanceComponent],
  template: `
    <app-terms-acceptance
      [programInfo]="programInfo"
      (termsAccepted)="onAcceptTerms()"
      (backClicked)="onCancel()"
      (navigationClicked)="onNavigate($event)">
    </app-terms-acceptance>
  `,
  styles: []
})
export class AwardsTermsAcceptanceComponent implements OnInit {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';
  partnerId = '';

  programInfo = {
    id: 'leroymer<PERSON>',
    name: '<PERSON>',
    logo: 'LEROY MERLIN',
    backgroundColor: '#66bb6a'
  };

  constructor(
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.partnerId = params['partnerId'] || 'pna';
      // Update programInfo based on partnerId
      const partnerInfo = this.getPartnerInfo(this.partnerId);
      this.programInfo = {
        id: this.partnerId,
        name: partnerInfo.name,
        logo: partnerInfo.logo,
        backgroundColor: partnerInfo.color
      };
    });
  }

  private getPartnerInfo(partnerId: string): { name: string, logo: string, color: string } {
    const partners: { [key: string]: { name: string, logo: string, color: string } } = {
      'pna': { name: 'Pick n Pay', logo: 'PNA', color: '#e53935' },
      'buildit': { name: 'Build It', logo: 'Build IT', color: '#c62828' },
      'leroymerlin': { name: 'Leroy Merlin', logo: 'LEROY MERLIN', color: '#66bb6a' }
    };
    return partners[partnerId] || partners['pna'];
  }

  onAcceptTerms() {
    this.router.navigate(['/public/awards/enrollment-confirmation'], {
      queryParams: { partnerId: this.partnerId }
    });
  }

  onCancel() {
    this.router.navigate(['/public/awards/program-selection']);
  }

  onNavigate(route: any) {
    switch(route) {
      case 'home':
        this.router.navigate(['/public/awards/dashboard']);
        break;
      case 'profile':
        // Navigate to profile when implemented
        break;
      case 'card':
        this.router.navigate(['/public/awards/partner-card']);
        break;
      case 'shops':
        this.router.navigate(['/public/awards/program-selection']);
        break;
      case 'transactions':
        this.router.navigate(['/public/awards/transaction']);
        break;
      default:
        break;
    }
  }
}