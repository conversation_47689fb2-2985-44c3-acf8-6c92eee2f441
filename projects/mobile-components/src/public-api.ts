/*
 * Public API Surface of mobile-components
 */

// Export for ComponentsModule and all its components
export * from './lib/components.module';


// Export Mobile-Optimized Components
export * from './lib/base/mobile-grid/mobile-grid.component';
export * from './lib/base/mobile-navigation/mobile-navigation.component';
export * from './lib/base/mobile-modal/mobile-modal.component';
export * from './lib/base/mobile-swipe-card/mobile-swipe-card.component';


// Base Components & Module (needed for public API)
export * from './lib/base/accordian/accordian.component';
export * from './lib/base/autocomplete/autocomplete.component';
export * from './lib/base/autocomplete-item/autocomplete-item.component';
export * from './lib/base/avatar/avatar.component';
export * from './lib/base/breadcrumb/breadcrumb.component';
export * from './lib/base/button/button.component';
export * from './lib/base/button-action/button-action.component';
export * from './lib/base/button-close/button-close.component';
export * from './lib/base/button-icon/button-icon.component';
export * from './lib/base/card/card.component';
export * from './lib/base/checkbox/checkbox.component';
export * from './lib/base/checkbox-animated/checkbox-animated.component';
export * from './lib/base/datepicker/datepicker.component';
export * from './lib/base/divider/divider.component';
export * from './lib/base/dropdown/dropdown.component';
export * from './lib/base/dropdown-divider/dropdown-divider.component';
export * from './lib/base/dropdown-item/dropdown-item.component';
export * from './lib/base/heading/heading.component';
export * from './lib/base/icon/icon.component';
export * from './lib/base/image/image.component';
export * from './lib/base/input/input.component';
export * from './lib/base/kbd/kbd.component';
export * from './lib/base/link/link.component';
export * from './lib/base/list/list.component';
export * from './lib/base/listbox/listbox.component';
export * from './lib/base/logo/logo.component';
export * from './lib/base/message/message.component';
export * from './lib/base/mobile/mobile.component';
export * from './lib/base/object/object.component';
export * from './lib/base/pagination/pagination.component';
export * from './lib/base/paragraph/paragraph.component';
export * from './lib/base/picture/picture.component';
export * from './lib/base/placeholder-page/placeholder-page.component';
export * from './lib/base/placeload/placeload.component'; // Exports BasePlaceloadComponent
export * from './lib/base/progress/progress.component';
export { ProseComponent } from './lib/base/prose/prose.component';
export { RadioComponent } from './lib/base/radio/radio.component';
export { SelectComponent } from './lib/base/select/select.component';
export { SnackComponent } from './lib/base/snack/snack.component';
export * from './lib/base/switch-ball/switch-ball.component';
export * from './lib/base/switch-thin/switch-thin.component';
export * from './lib/base/table/table.component';
export * from './lib/base/tabs/tabs.component';
export * from './lib/base/tag/tag.component';
export * from './lib/base/text/text.component';
export * from './lib/base/textarea/textarea.component';
export * from './lib/base/base.module'; // Export the module itself

// Export Layout components needed publicly
export * from './lib/layout/layout.module'; // Also export the module itself
export * from './lib/layout/app-layout-switcher/app-layout-switcher.component';
export * from './lib/layout/head-logo/head-logo.component';
export * from './lib/layout/layout.component';
// TEMPORARILY DISABLED DUE TO TEMPLATE PARSING ISSUES
// export * from './lib/layout/navigation-top/navigation-top.component';

// Export Shared components needed publicly
export * from './lib/shared/abstract.form.component';

// Export Widgets components needed publicly
export { DatepickerComponent as WidgetsDatepickerComponent } from './lib/widgets/datepicker/datepicker.component'; // Alias to avoid conflict
export * from './lib/widgets/widgets.module';
export * from './lib/widgets/company-overview/company-overview.component';
export * from './lib/widgets/button-group/button-group.component';
export * from './lib/widgets/flex-table-start/flex-table-start.component';
export * from './lib/widgets/image-gallery/image-gallery.component';
export * from './lib/widgets/filter/filter.component';
export * from './lib/widgets/tabbed-content/tabbed-content.component';
export * from './lib/widgets/icon-text/icon-text.component';
export * from './lib/widgets/flex-table-cell/flex-table-cell.component';
export * from './lib/widgets/icon-box/icon-box.component';
export * from './lib/widgets/search-compact/search-compact.component';
export * from './lib/widgets/modal-medium-tier/modal-medium-tier.component';
export * from './lib/widgets/authors-list-compact/authors-list-compact.component';
export * from './lib/widgets/avatar-group/avatar-group.component';
export * from './lib/widgets/image-links/image-links.component';
export * from './lib/widgets/menu-icon-list/menu-icon-list.component';
export * from './lib/widgets/list-item/list-item.component';
export * from './lib/widgets/map-marker/map-marker.component';
export * from './lib/widgets/card-filters/card-filters.component';
export * from './lib/widgets/action-text/action-text.component';
export * from './lib/widgets/timeline-compact/timeline-compact.component';
export * from './lib/widgets/modal-footer/modal-footer.component';
export * from './lib/widgets/welcome/welcome.component';
export * from './lib/widgets/info-image/info-image.component';
export * from './lib/widgets/modal-large-tier/modal-large-tier.component';
export * from './lib/widgets/placeholder-minimal/placeholder-minimal.component';
export * from './lib/widgets/info-badges/info-badges.component';
export * from './lib/widgets/features/features.component';
export * from './lib/widgets/vcard-right/vcard-right.component';
export * from './lib/widgets/modal-small-tier/modal-small-tier.component';
export * from './lib/widgets/flex-table-heading/flex-table-heading.component';
export * from './lib/widgets/flex-table-row/flex-table-row.component';
export * from './lib/widgets/flex-table-wrapper/flex-table-wrapper.component';
export * from './lib/widgets/followers-compact/followers-compact.component';
export * from './lib/widgets/inbox-message/inbox-message.component';
export * from './lib/widgets/video-compact/video-compact.component';
export * from './lib/widgets/tag-list-compact/tag-list-compact.component';
export * from './lib/widgets/file-list-tabbed/file-list-tabbed.component';
export * from './lib/widgets/avatar-group-id/avatar-group-id.component';
export * from './lib/widgets/comment-list-compact/comment-list-compact.component';
export * from './lib/widgets/fullscreen-dropfile/fullscreen-dropfile.component';
export * from './lib/widgets/placeholder-compact/placeholder-compact.component';
export * from './lib/widgets/progress-circle/progress-circle.component';
export * from './lib/widgets/account-balance/account-balance.component';
export { DynamicListComponent } from './lib/widgets/dynamic-list/dynamic-list.component';
export * from './lib/widgets/flex-table/flex-table.component';
export * from './lib/widgets/focus-loop/focus-loop.component';
export * from './lib/widgets/listbox-item/listbox-item.component';
export * from './lib/widgets/quill/quill.component';
export * from './lib/widgets/search/search.component';
export * from './lib/widgets/search-tag/search-tag.component';
export { SelectMultiComponent } from './lib/widgets/select-multi/select-multi.component';
export * from './lib/widgets/tab-slider/tab-slider.component';
export * from './lib/widgets/tags/tags.component';
export * from './lib/widgets/tree-select/tree-select.component';
export * from './lib/widgets/tree-select-item/tree-select-item.component';
export * from './lib/widgets/upload/upload.component';
export * from './lib/widgets/upload-avatar/upload-avatar.component';
export * from './lib/widgets/upload-input/upload-input.component';

// Forms
export * from './lib/forms/forms.module';
export * from './lib/forms/radio-headless/radio-headless.component';
export * from './lib/forms/checkbox-headless/checkbox-headless.component';
export * from './lib/forms/input-file-path/input-file-path.component';
export * from './lib/forms/input-file/input-file.component';
export * from './lib/forms/input-number/input-number.component';
export * from './lib/forms/otp/otp-validator.component';
export * from './lib/forms/address/address.component';
export * from './lib/forms/signup/signup.component';
export * from './lib/forms/validate/validate.component';
export * from './lib/forms/countries-select/countries-select.component';
export * from './lib/forms/industry-select/industry-select.component';
export { InputFileHeadlessComponent } from './lib/forms/input-file-headless/input-file-headless.component';
export * from './lib/forms/otp/otp.component';

// Pages
export * from './lib/pages/pages.module';
export * from './lib/pages/customizer/pages-customizer.component';
export * from './lib/pages/dynamic-dashboard/dynamic-dashboard.component';
export * from './lib/pages/home/<USER>';
export * from './lib/pages/profile-details/profile-details.component';
export * from './lib/pages/login/themes/theme1/pages-login-theme1.component';
export * from './lib/pages/landing/themes/theme1/pages-landing-theme1.component';
export * from './lib/pages/landing/themes/games/games.component';
export * from './lib/pages/dynamic/games/dashboard/dashboard.component';
export { GamesSingleComponent } from './lib/pages/dynamic/games/single/single.component'; // Direct export for GamesSingleComponent
export { AllGamesComponent } from './lib/pages/dynamic/games/all/all.component'; // Export AllGamesComponent
export { GamesCategoriesComponent } from './lib/pages/dynamic/games/categories/categories.component'; // Direct export without aliasing

// Features
export * from './lib/features/features.module';
export * from './lib/features/authentication/authentication.module';
export * from './lib/features/calendar/calendar.module';
export * from './lib/features/cart/cart.module';
export * from './lib/features/chat/chat.module';
export * from './lib/features/crypto/crypto.module';
export * from './lib/features/customizer/customizer.module';
export * from './lib/features/geolocation/geolocation.module';
export * from './lib/features/investments/investments.module';
export * from './lib/features/invites/invites.module';
export * from './lib/features/leagues/leagues.module';
export * from './lib/features/notifications/notifications.module';
export * from './lib/features/offers/offers.module';
export * from './lib/features/payments/payments.module';
export * from './lib/features/products/products.module';
export * from './lib/features/profile/profile.module';
export * from './lib/features/projects/projects.module';
export * from './lib/features/search/search.module';
export * from './lib/features/settings/settings.module';
export * from './lib/features/skills/skills.module';
export * from './lib/features/socials/socials.module';
export * from './lib/features/support/support.module';
export * from './lib/features/teams/teams.module';
export * from './lib/features/todos/todos.module';
export * from './lib/features/topics/topics.module';
export * from './lib/features/transactions/transactions.module';
export * from './lib/features/users/users.module';
export * from './lib/features/virtual-card/virtual-card.module';
export * from './lib/features/virtual-card/virtualcard/virtualcard.component';
export * from './lib/features/games/games/games.module';
export * from './lib/features/games/games/index';
export * from './lib/features/games/games/components/games-score/games-score.component';
export * from './lib/features/games/games/components/games-header/games-header.component';
export * from './lib/features/games/games/crossword/crossword.component';
export * from './lib/features/games/games/components/win-lose-overlay/win-lose-overlay.component';
export * from './lib/features/games/games/wheel-spin/wheel-spin.component';
export * from './lib/features/games/games/blackjack/blackjack.component';
export * from './lib/features/games/games/minesweeper/minesweeper.component';
export * from './lib/features/games/games/solitaire/solitaire.component';
export * from './lib/features/games/games/candy-crush/candy-crush.component';
export * from './lib/features/games/games/treasure-hunt/treasure-hunt.component';
// Features Components & Modules (needed for public API)
export * from './lib/features/cart/shopping-cart-compact/shopping-cart-compact.component';
export * from './lib/features/authentication/password/password.component';
export * from './lib/features/authentication/pin/pin.component';
export { SecurityComponent } from './lib/features/authentication/security/security.component';
export { SecureComponent } from './lib/features/authentication/secure/secure.component';
export * from './lib/features/calendar/calendar-event/calendar-event.component';
export * from './lib/features/calendar/calendar-event-pending/calendar-event-pending.component';
export * from './lib/features/calendar/days-square/days-square.component';
export * from './lib/features/chat/chat/chat.component';
export * from './lib/features/crypto/popular-cryptos/popular-cryptos.component';
export * from './lib/features/customizer/customizer/customizer.component';
export * from './lib/features/geolocation/geo/geo.component';
export * from './lib/features/geolocation/geo-location/geo-location.component';
export * from './lib/features/investments/invest/invest.component';
export * from './lib/features/invites/filter-invite/filter-invite.component';
export * from './lib/features/leagues/league-list-compact/league-list-compact.component';
export * from './lib/features/notifications/notifications/notifications.component';
export * from './lib/features/notifications/notifications-compact/notifications-compact.component';
export * from './lib/features/offers/offer-collapse/offer-collapse.component';
export * from './lib/features/payments/credit-card/credit-card.component';
export * from './lib/features/payments/credit-card-real/credit-card-real.component';
export * from './lib/features/payments/credit-card-small/credit-card-small.component';
export * from './lib/features/products/product-compact/product-compact.component';
export * from './lib/features/products/products/products.component';
export * from './lib/features/profile/profile/profile.component';
export * from './lib/features/profile/profileremove/profileremove.component';
export * from './lib/features/projects/project-list-compact/project-list-compact.component';
export * from './lib/features/search/app-search/app-search.component';
export * from './lib/features/search/app-search-result/app-search-result.component';
export * from './lib/features/settings/settings/settings.component';
export * from './lib/features/settings/theme-switch/theme-switch.component';
export * from './lib/features/settings/theme-toggle/theme-toggle.component';
export * from './lib/features/skills/trending-skills/trending-skills.component';
export * from './lib/features/socials/social-links/social-links.component';
export * from './lib/features/support/contact/contact.component';
export * from './lib/features/support/contactus/contactus.component';
export * from './lib/features/support/info/info.component';
export * from './lib/features/support/pending-tickets/pending-tickets.component';
export * from './lib/features/teams/team-list-compact/team-list-compact.component';
export * from './lib/features/teams/team-search-compact/team-search-compact.component';
export * from './lib/features/todos/todo-list-compact/todo-list-compact.component';
export * from './lib/features/todos/todo-list-tabbed/todo-list-tabbed.component';
export * from './lib/features/topics/topic-list-compact/topic-list-compact.component';
export * from './lib/features/transactions/money-in/money-in.component';
export * from './lib/features/transactions/money-out/money-out.component';
export * from './lib/features/transactions/statements/statements.component';
export * from './lib/features/transactions/transaction-compact/transaction-compact.component';
export * from './lib/features/transactions/transaction-summary/transaction-summary.component';
export * from './lib/features/transactions/transactions/transactions.component';
export { TransactionsFiltersComponent } from './lib/features/transactions/transactions-filters/transactions-filters.component';
export * from './lib/features/transactions/transactions-list-placeload/transactions-list-placeload.component';
export * from './lib/features/users/user-list/user-list.component';
export * from './lib/features/virtual-card/virtual/virtual.component';
export * from './lib/features/virtual-card/virtualcard/virtualcard.component';
export * from './lib/shared/abstract.component';

export * from './lib/features/games/games/submit-selfie/submit-selfie.component';

export * from './lib/base/container/container.component';

// Notifications
export * from './lib/notifications/models/notification.models';
export * from './lib/notifications/models/notification-analytics.models';
export * from './lib/notifications/models/notification-admin.models';
export * from './lib/notifications/services/notification-analytics.service';
export * from './lib/notifications/services/notification-admin.service';
export * from './lib/notifications/notification-permission-prompt/notification-permission-prompt.component';
export * from './lib/notifications/notification-preferences/notification-preferences.component';
export * from './lib/notifications/notification-preview/notification-preview.component';
export * from './lib/notifications/notification-template-form/notification-template-form.component';

// Page Sections
export * from './lib/page-sections/page-sections.module';
export * from './lib/page-sections/headers/home-header/home-header.component';
export { DashboardHeaderComponent } from './lib/page-sections/headers/dashboard-header/dashboard-header.component';
export * from './lib/page-sections/headers/profile-header/profile-header.component';
export * from './lib/page-sections/navigation/home-navigation/home-navigation.component';
export * from './lib/page-sections/navigation/profile-settings/profile-settings.component';
export * from './lib/page-sections/actions/home-actions/home-actions.component';
export * from './lib/page-sections/actions/dashboard-quick-actions/dashboard-quick-actions.component';
export * from './lib/page-sections/content/dashboard-welcome/dashboard-welcome.component';
export * from './lib/page-sections/content/dashboard-summary/dashboard-summary.component';
export * from './lib/page-sections/content/profile-form/profile-form.component';
export * from './lib/page-sections/content/profile-help/profile-help.component';

// Awards Feature
export * from './lib/features/awards/awards.module';
export * from './lib/features/awards/otp-request/otp-request.component';
export * from './lib/features/awards/otp-verification/otp-verification.component';
export * from './lib/features/awards/registration-form/registration-form.component';
export { AwardsLoginComponent } from './lib/features/awards/login/login.component';
export { AwardsDashboardComponent } from './lib/features/awards/dashboard/dashboard.component';
export * from './lib/features/awards/balance-detail/balance-detail.component';
export * from './lib/features/awards/bottom-navigation/bottom-navigation.component';
export * from './lib/features/awards/partner-card/partner-card.component';
export * from './lib/features/awards/qr-card/qr-card.component';
export * from './lib/features/awards/program-selection/program-selection.component';
export * from './lib/features/awards/terms-acceptance/terms-acceptance.component';
export * from './lib/features/awards/enrollment-confirmation/enrollment-confirmation.component';
export * from './lib/features/awards/transaction-flow/transaction-flow.component';

// Services
export * from './lib/services/awards-auth.service';
export * from './lib/services/awards-programs.service';
