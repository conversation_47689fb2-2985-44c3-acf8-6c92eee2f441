import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, NavigationEnd } from '@angular/router';
import { AwardsDashboardComponent as MobileDashboardComponent, AwardsAuthService, AwardsProgramsService } from '@projects/mobile-components';
import { Subject, takeUntil } from 'rxjs';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-awards-dashboard-page',
  standalone: true,
  imports: [CommonModule, MobileDashboardComponent],
  template: `
    <app-awards-dashboard
      [user]="user"
      [enrolledPrograms]="enrolledPrograms"
      (partnerClicked)="onSelectPartner($event)"
      (navigationClicked)="onNavigate($event)"
      (balanceClicked)="onBalanceClick()">
    </app-awards-dashboard>
  `,
  styles: []
})
export class AwardsDashboardComponent implements OnInit, OnDestroy {
  userId = localStorage.getItem('awardsUserId') || 'USER_DEFAULT';
  enrolledPrograms: any[] = [];
  private destroy$ = new Subject<void>();

  user = {
    name: '<PERSON>',
    balance: 0,
    currency: 'R'
  };

  constructor(
    private router: Router,
    private awardsAuth: AwardsAuthService,
    private programsService: AwardsProgramsService
  ) {
    // Listen for navigation events to refresh enrolled programs
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        filter((event: NavigationEnd) => event.url.includes('/awards/dashboard')),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.loadEnrolledPrograms();
      });
  }

  ngOnInit() {
    const currentUser = this.awardsAuth.getCurrentUser();
    if (currentUser) {
      // For demo purposes, generate a random balance between 50000 and 100000
      const demoBalance = Math.floor(Math.random() * 50000) + 50000;
      
      this.user = {
        name: currentUser.name || 'User',
        balance: demoBalance,
        currency: 'R'
      };
      
      this.userId = currentUser.id;
      this.loadEnrolledPrograms();
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadEnrolledPrograms() {
    if (this.userId) {
      this.enrolledPrograms = this.programsService.getEnrolledPrograms(this.userId);
      console.log('Dashboard - Loading enrolled programs for user:', this.userId, 'Programs:', this.enrolledPrograms);
    }
  }

  onSelectPartner(partnerId: string) {
    this.router.navigate(['/public/awards/balance-detail'], {
      queryParams: { partnerId: partnerId }
    });
  }

  onBalanceClick() {
    // Handle balance click - could navigate to balance details or show more info
    console.log('Balance clicked');
  }

  onNavigate(route: any) {
    switch(route) {
      case 'profile':
        // Navigate to profile when implemented
        break;
      case 'card':
        this.router.navigate(['/public/awards/partner-card']);
        break;
      case 'shops':
        this.router.navigate(['/public/awards/program-selection']);
        break;
      case 'transactions':
        this.router.navigate(['/public/awards/transaction']);
        break;
      default:
        break;
    }
  }
}